const DEV = false; // means production

//false; //means production
//true; //means development

const PRO_BASE_URL = import.meta.env.VITE_PRO_BASE_URL;
const DEV_BASE_URL = import.meta.env.VITE_DEV_BASE_URL;

const PRO_AI_BASE_URL = import.meta.env.VITE_PRO_AI_BASE_URL;
const DEV_AI_BASE_URL = import.meta.env.VITE_DEV_AI_BASE_URL;

// Stripe Configuration
const STRIPE_PUBLISHABLE_KEY_TEST = import.meta.env
  .VITE_STRIPE_PUBLISHABLE_KEY_TEST;
// const STRIPE_PUBLISHABLE_KEY_LIVE = import.meta.env
//   .VITE_STRIPE_PUBLISHABLE_KEY_LIVE;

export const BASE_URL = DEV ? DEV_BASE_URL : PRO_BASE_URL;

// Ensure AI_BASE_URL is always HTTPS in production
const rawAiBaseUrl = DEV ? DEV_AI_BASE_URL : PRO_AI_BASE_URL;

// Debug logging
console.log('🔍 Config Debug:');
console.log('DEV:', DEV);
console.log('rawAiBaseUrl:', rawAiBaseUrl);
console.log('PRO_AI_BASE_URL from env:', PRO_AI_BASE_URL);

// Force HTTPS for AI_BASE_URL - multiple fallbacks to ensure HTTPS
let secureAiUrl = rawAiBaseUrl;

// Method 1: Replace http:// with https://
if (secureAiUrl?.startsWith('http://')) {
  secureAiUrl = secureAiUrl.replace('http://', 'https://');
}

// Method 2: If still no protocol, add https://
if (secureAiUrl && !secureAiUrl.startsWith('http')) {
  secureAiUrl = `https://${secureAiUrl}`;
}

// Method 3: Hardcode fallback for production
if (!DEV && (!secureAiUrl || secureAiUrl.includes('http://'))) {
  secureAiUrl = 'https://neuquipai.cogweel.com';
  console.warn('🚨 Using hardcoded HTTPS URL as fallback');
}

export const AI_BASE_URL = secureAiUrl;

console.log('Final AI_BASE_URL:', AI_BASE_URL);

// export const STRIPE_PUBLISHABLE_KEY = DEV
//   ? STRIPE_PUBLISHABLE_KEY_TEST
//   : STRIPE_PUBLISHABLE_KEY_LIVE;

export const STRIPE_PUBLISHABLE_KEY = STRIPE_PUBLISHABLE_KEY_TEST;
